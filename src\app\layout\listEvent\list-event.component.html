<div class="events-container">
  <div class="header">
    <h1>Liste des Événements</h1>
    
    <!-- Barre de recherche -->
    <div class="search-container">
      <input 
        type="text" 
        class="search-input" 
        placeholder="Rechercher par titre ou lieu..." 
        [(ngModel)]="searchTerm" 
        (input)="onSearchChange()">
      <i class="search-icon">🔍</i>
    </div>
  </div>

  <!-- Liste des événements -->
  <div class="events-grid">
    <div class="event-card" *ngFor="let event of filteredEvents">
      <!-- Image de l'événement -->
      <div class="event-image">
        <img [src]="event.imageUrl" [alt]="event.title" />
        <div class="event-price">{{ event.price }}€</div>
      </div>

      <!-- Contenu de la carte -->
      <div class="event-content">
        <h3 class="event-title">{{ event.title }}</h3>
        <p class="event-description">{{ event.description }}</p>
        
        <div class="event-details">
          <div class="event-info">
            <span class="info-label">📅 Date:</span>
            <span class="info-value">{{ formatDate(event.date) }}</span>
          </div>
          
          <div class="event-info">
            <span class="info-label">📍 Lieu:</span>
            <span class="info-value">{{ event.location }}</span>
          </div>
          
          <div class="event-info">
            <span class="info-label">🎫 Disponibilité:</span>
            <span class="info-value" [class]="getAvailabilityClass(event)">
              {{ getAvailabilityText(event) }}
            </span>
          </div>
        </div>

        <!-- Actions -->
        <div class="event-actions">
          <button 
            class="like-btn" 
            [disabled]="isEventExpired(event)"
            [class.disabled]="isEventExpired(event)"
            (click)="onLike(event)">
            ❤️ {{ event.nbrLike }}
          </button>
          
          <div class="event-status" *ngIf="isEventExpired(event)">
            <span class="expired-badge">Événement expiré</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Message si aucun événement trouvé -->
  <div class="no-events" *ngIf="filteredEvents.length === 0">
    <p>Aucun événement trouvé pour "{{ searchTerm }}"</p>
  </div>
</div>
