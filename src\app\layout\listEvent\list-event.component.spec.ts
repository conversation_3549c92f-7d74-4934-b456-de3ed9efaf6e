import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { ListEventComponent } from './list-event.component';

describe('ListEventComponent', () => {
  let component: ListEventComponent;
  let fixture: ComponentFixture<ListEventComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ListEventComponent],
      imports: [FormsModule]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ListEventComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with 2 events', () => {
    expect(component.events.length).toBe(2);
    expect(component.filteredEvents.length).toBe(2);
  });

  it('should increment like count when onLike is called for non-expired event', () => {
    const futureEvent = component.events[0]; // Angular Summit is in 2025
    const initialLikes = futureEvent.nbrLike;
    
    component.onLike(futureEvent);
    
    expect(futureEvent.nbrLike).toBe(initialLikes + 1);
  });

  it('should not increment like count for expired event', () => {
    // Create a past event
    const pastEvent = {
      id: 3,
      title: 'Past Event',
      description: 'This event has passed',
      date: new Date('2020-01-01'),
      location: 'Past Location',
      price: 10,
      organizerId: 103,
      imageUrl: 'test.jpg',
      nbPlaces: 10,
      nbrLike: 5
    };
    
    const initialLikes = pastEvent.nbrLike;
    component.onLike(pastEvent);
    
    expect(pastEvent.nbrLike).toBe(initialLikes);
  });

  it('should filter events by search term', () => {
    component.searchTerm = 'Angular';
    component.onSearchChange();
    
    expect(component.filteredEvents.length).toBe(1);
    expect(component.filteredEvents[0].title).toBe('Angular Summit');
  });

  it('should filter events by location', () => {
    component.searchTerm = 'Tunis';
    component.onSearchChange();
    
    expect(component.filteredEvents.length).toBe(1);
    expect(component.filteredEvents[0].location).toBe('Tunis');
  });

  it('should show all events when search term is empty', () => {
    component.searchTerm = '';
    component.onSearchChange();
    
    expect(component.filteredEvents.length).toBe(2);
  });

  it('should return correct availability text', () => {
    const availableEvent = component.events[0]; // has 25 places
    const soldOutEvent = component.events[1]; // has 0 places
    
    expect(component.getAvailabilityText(availableEvent)).toBe('Places disponibles : 25');
    expect(component.getAvailabilityText(soldOutEvent)).toBe('Complet');
  });

  it('should return correct availability class', () => {
    const availableEvent = component.events[0]; // has 25 places
    const soldOutEvent = component.events[1]; // has 0 places
    
    expect(component.getAvailabilityClass(availableEvent)).toBe('available');
    expect(component.getAvailabilityClass(soldOutEvent)).toBe('sold-out');
  });
});
