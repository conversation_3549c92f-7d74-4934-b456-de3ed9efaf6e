  import { NgModule } from '@angular/core';
  import { BrowserModule } from '@angular/platform-browser';
  import { FormsModule } from '@angular/forms';

  import { AppRoutingModule } from './app-routing.module';
  import { AppComponent } from './app.component';
  import { NavbarComponent } from './layout/navbar/navbar.component';
  import { FooterComponent } from './layout/footer/footer.component';
  import { HomeComponent } from './layout/home/<USER>';
  import { ListEventComponent } from './layout/listEvent/list-event.component';


  @NgModule({
    declarations: [
      AppComponent,
      NavbarComponent,
      FooterComponent,
      HomeComponent,
      ListEventComponent
    ],
    imports: [
      BrowserModule,
      AppRoutingModule,
      FormsModule
    ],
    providers: [],
    bootstrap: [AppComponent]
  })
  export class AppModule { }
