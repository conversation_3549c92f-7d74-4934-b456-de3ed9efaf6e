/* Hero Section */
.hero {
  background: linear-gradient(135deg, #007bff, #6610f2);
  color: white;
  text-align: center;
  padding: 80px 20px;
}

.hero-content h1 {
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
}

.btn-start {
  background-color: #fff;
  color: #007bff;
  border: none;
  padding: 12px 25px;
  font-size: 1rem;
  border-radius: 5px;
  cursor: pointer;
  transition: 0.3s;
}

.btn-start:hover {
  background-color: #f8f9fa;
}

/* Section About */
.about {
  padding: 50px 20px;
  text-align: center;
}

.about .container {
  max-width: 800px;
  margin: 0 auto;
}

.about h2 {
  font-size: 2rem;
  margin-bottom: 15px;
}

/* Features Section */
.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.feature-card {
  background: #f8f9fa;
  padding: 30px 20px;
  border-radius: 10px;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.feature-card h3 {
  font-size: 1.3rem;
  margin-bottom: 15px;
  color: #333;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 2rem;
  }

  .hero-content p {
    font-size: 1rem;
  }

  .features {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}
