/* Hero Section */
.hero {
  background: linear-gradient(135deg, #007bff, #6610f2);
  color: white;
  text-align: center;
  padding: 80px 20px;
}

.hero-content h1 {
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
}

.btn-start {
  background-color: #fff;
  color: #007bff;
  border: none;
  padding: 12px 25px;
  font-size: 1rem;
  border-radius: 5px;
  cursor: pointer;
  transition: 0.3s;
}

.btn-start:hover {
  background-color: #f8f9fa;
}

/* Section About */
.about {
  padding: 50px 20px;
  text-align: center;
}

.about .container {
  max-width: 800px;
  margin: 0 auto;
}

.about h2 {
  font-size: 2rem;
  margin-bottom: 15px;
}
