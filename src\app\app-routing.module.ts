import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from './layout/home/<USER>';
import { ListEventComponent } from './layout/listEvent/list-event.component';

const routes: Routes = [
  { path: '', component: HomeComponent },
  { path: 'events', component: ListEventComponent },
  { path: '**', redirectTo: '' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
