.events-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: #333;
  margin-bottom: 20px;
  font-size: 2.5rem;
  font-weight: 300;
}

/* Barre de recherche */
.search-container {
  position: relative;
  max-width: 400px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  padding: 12px 45px 12px 15px;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  font-size: 16px;
  outline: none;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  border-color: #007bff;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  color: #666;
}

/* Grille des événements */
.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 25px;
  margin-top: 30px;
}

/* Carte d'événement */
.event-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #f0f0f0;
}

.event-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Image de l'événement */
.event-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.event-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.event-card:hover .event-image img {
  transform: scale(1.05);
}

.event-price {
  position: absolute;
  top: 15px;
  right: 15px;
  background: #007bff;
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 14px;
}

/* Contenu de la carte */
.event-content {
  padding: 20px;
}

.event-title {
  color: #333;
  margin: 0 0 10px 0;
  font-size: 1.4rem;
  font-weight: 600;
}

.event-description {
  color: #666;
  margin: 0 0 20px 0;
  line-height: 1.5;
  font-size: 14px;
}

/* Détails de l'événement */
.event-details {
  margin-bottom: 20px;
}

.event-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-label {
  font-weight: 600;
  margin-right: 8px;
  min-width: 80px;
}

.info-value {
  color: #555;
}

.info-value.available {
  color: #28a745;
  font-weight: 600;
}

.info-value.sold-out {
  color: #dc3545;
  font-weight: 600;
}

/* Actions */
.event-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.like-btn {
  background: linear-gradient(45deg, #ff6b6b, #ee5a52);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.like-btn:hover:not(.disabled) {
  background: linear-gradient(45deg, #ff5252, #e53935);
  transform: scale(1.05);
}

.like-btn.disabled {
  background: #ccc;
  cursor: not-allowed;
  opacity: 0.6;
}

.expired-badge {
  background: #ffc107;
  color: #333;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 600;
}

/* Message aucun événement */
.no-events {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 18px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .events-container {
    padding: 15px;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .events-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .search-container {
    max-width: 100%;
  }
  
  .event-card {
    margin: 0 10px;
  }
}

@media (max-width: 480px) {
  .event-content {
    padding: 15px;
  }
  
  .event-title {
    font-size: 1.2rem;
  }
  
  .event-actions {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .like-btn {
    justify-content: center;
  }
}
