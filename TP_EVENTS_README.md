# TP Gestion d'Événements - Angular 17

## Description du Projet

Ce projet implémente un module de gestion d'événements dans une application Angular 17. Il permet d'afficher, filtrer et interagir avec une liste d'événements technologiques.

## Fonctionnalités Implémentées

### 1. Structure du Projet

- **Interface Event** (`src/app/models/event.ts`)
  - Définit la structure des données d'un événement
  - Champs : id, title, description, date, location, price, organizerId, imageUrl, nbPlaces, nbrLike

- **Composant ListEvent** (`src/app/layout/listEvent/`)
  - Composant principal pour l'affichage des événements
  - Gestion de la liste statique d'événements
  - Logique de filtrage et d'interaction

### 2. Fonctionnalités Utilisateur

#### Affichage des Événements
- **Cartes responsives** : Chaque événement est affiché dans une carte élégante
- **Informations complètes** : Titre, description, date formatée, lieu, prix, image
- **Statut des places** : Affichage "Complet" ou "Places disponibles : X"

#### Interactions
- **Bouton Like ❤️** : Incrémente le compteur de likes
- **Gestion des événements expirés** : Bouton désactivé pour les événements passés
- **Badge "Événement expiré"** : Indication visuelle pour les événements passés

#### Recherche et Filtrage
- **Barre de recherche** : Filtrage en temps réel par titre ou lieu
- **Two-way binding** : Utilisation de `[(ngModel)]` pour la synchronisation
- **Message d'absence** : Affichage quand aucun événement ne correspond à la recherche

### 3. Techniques Angular Utilisées

#### Data Binding
- **Interpolation `{{ }}`** : Affichage des propriétés des événements
- **Property Binding `[ ]`** : Liaison des propriétés (src, alt, class, disabled)
- **Event Binding `( )`** : Gestion des clics et événements de saisie
- **Two-way Binding `[(ngModel)]`** : Synchronisation bidirectionnelle pour la recherche

#### Directives Structurelles
- **`*ngFor`** : Itération sur la liste des événements filtrés
- **`*ngIf`** : Affichage conditionnel (badge expiré, message d'absence)

#### Routage
- **Configuration des routes** : Navigation entre accueil et liste des événements
- **Liens de navigation** : Navbar et page d'accueil avec `routerLink`
- **RouterOutlet** : Affichage dynamique des composants

### 4. Design et Responsive

#### CSS Moderne
- **Grid Layout** : Disposition responsive des cartes d'événements
- **Flexbox** : Alignement et distribution des éléments
- **Transitions CSS** : Animations fluides au survol
- **Variables CSS** : Cohérence des couleurs et espacements

#### Responsive Design
- **Mobile First** : Adaptation pour tous les écrans
- **Breakpoints** : Ajustements pour tablettes et mobiles
- **Grid responsive** : Colonnes adaptatives selon la taille d'écran

### 5. Données de Test

Deux événements de démonstration :

1. **Angular Summit**
   - Date : 10 novembre 2025 (futur)
   - Lieu : Tunis
   - Prix : 50€
   - Places : 25 disponibles

2. **Web Dev Days**
   - Date : 5 janvier 2025 (futur)
   - Lieu : Ariana
   - Prix : 30€
   - Places : Complet (0 places)

## Installation et Lancement

```bash
# Installation des dépendances
npm install

# Lancement du serveur de développement
ng serve

# Accès à l'application
http://localhost:4200
```

## Navigation

- **Page d'accueil** : `/` - Présentation de l'application avec lien vers les événements
- **Liste des événements** : `/events` - Affichage et gestion des événements

## Tests

```bash
# Lancement des tests unitaires
ng test

# Tests spécifiques au composant ListEvent
ng test --include="**/list-event.component.spec.ts"
```

## Structure des Fichiers

```
src/app/
├── models/
│   └── event.ts                    # Interface Event
├── layout/
│   ├── listEvent/
│   │   ├── list-event.component.ts     # Logique du composant
│   │   ├── list-event.component.html   # Template HTML
│   │   ├── list-event.component.css    # Styles CSS
│   │   └── list-event.component.spec.ts # Tests unitaires
│   ├── home/                       # Page d'accueil mise à jour
│   └── navbar/                     # Navigation mise à jour
├── app-routing.module.ts           # Configuration des routes
└── app.module.ts                   # Module principal avec FormsModule
```

## Fonctionnalités Avancées

### Gestion des Dates
- **Formatage français** : Affichage des dates en format local
- **Comparaison de dates** : Détection automatique des événements expirés
- **Logique métier** : Désactivation des interactions pour les événements passés

### Performance
- **Filtrage optimisé** : Recherche insensible à la casse
- **Gestion d'état** : Séparation entre données originales et filtrées
- **Lazy loading** : Prêt pour l'intégration de données asynchrones

### Accessibilité
- **Labels sémantiques** : Utilisation appropriée des balises HTML
- **Contraste** : Couleurs respectant les standards d'accessibilité
- **Navigation clavier** : Support des interactions clavier

## Évolutions Possibles

1. **Backend Integration** : Connexion à une API REST
2. **Authentification** : Système de connexion utilisateur
3. **CRUD Complet** : Création, modification, suppression d'événements
4. **Pagination** : Gestion de grandes listes d'événements
5. **Filtres Avancés** : Par date, prix, catégorie
6. **Géolocalisation** : Carte interactive des événements
7. **Notifications** : Rappels pour les événements favoris
